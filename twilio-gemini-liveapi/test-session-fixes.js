#!/usr/bin/env node

/**
 * Session Handling Fixes Test Runner
 * 
 * Comprehensive test runner for validating all 4 flows with real integrations:
 * 1. Outbound Twilio calls
 * 2. Inbound Twilio calls  
 * 3. Outbound browser testing
 * 4. Inbound browser testing
 * 
 * Tests the specific fixes implemented:
 * ✅ Twilio Audio Streaming - Sequence Numbers
 * ✅ Standardized Instruction Sending  
 * ✅ Inbound Configuration Loading
 * ✅ Testing Flow Consistency
 * ✅ Security & Cleanup
 */

import { spawn } from 'child_process';
import { performance } from 'perf_hooks';
import fs from 'fs';
import path from 'path';

const TEST_SERVER_PORT = process.env.PORT || 3101;
const BASE_URL = `http://localhost:${TEST_SERVER_PORT}`;

class TestRunner {
    constructor() {
        this.results = {
            total: 0,
            passed: 0,
            failed: 0,
            skipped: 0,
            errors: [],
            performance: {},
            startTime: null,
            endTime: null
        };
        this.serverProcess = null;
        this.serverReady = false;
    }

    async run() {
        console.log('🚀 SESSION HANDLING FIXES - COMPREHENSIVE TEST SUITE');
        console.log('====================================================');
        console.log(`📅 Started at: ${new Date().toISOString()}`);
        console.log(`🌐 Server URL: ${BASE_URL}`);
        console.log('====================================================\n');

        this.results.startTime = performance.now();

        try {
            // Step 1: Check if server is already running
            await this.checkServerStatus();
            
            // Step 2: Run the session handling fixes tests
            await this.runSessionFixesTests();
            
            // Step 3: Run the comprehensive workflow tests
            await this.runWorkflowTests();
            
            // Step 4: Run validation tests
            await this.runValidationTests();
            
            // Step 5: Generate report
            await this.generateReport();
            
        } catch (error) {
            console.error('❌ Test runner failed:', error.message);
            this.results.errors.push({
                test: 'test-runner',
                error: error.message,
                stack: error.stack
            });
        } finally {
            this.results.endTime = performance.now();
            this.printSummary();
        }
    }

    async checkServerStatus() {
        console.log('🔍 Checking server status...');
        
        try {
            const response = await fetch(`${BASE_URL}/health`);
            if (response.ok) {
                this.serverReady = true;
                const health = await response.json();
                console.log(`✅ Server is running (status: ${health.status || 'ok'})`);
            } else {
                throw new Error(`Server returned ${response.status}`);
            }
        } catch (error) {
            console.log(`⚠️ Server not available: ${error.message}`);
            console.log('ℹ️ Tests will run but may have limited functionality');
            this.serverReady = false;
        }
        console.log('');
    }

    async runSessionFixesTests() {
        console.log('🧪 Running Session Handling Fixes Tests...');
        console.log('------------------------------------------');
        
        const testResult = await this.runNodeTest('test/session-handling-fixes.test.js');
        this.processTestResult('Session Fixes', testResult);
        console.log('');
    }

    async runWorkflowTests() {
        console.log('🔄 Running Workflow Integration Tests...');
        console.log('----------------------------------------');
        
        const testResult = await this.runNodeTest('test/workflow-integration.test.js');
        this.processTestResult('Workflow Integration', testResult);
        console.log('');
    }

    async runValidationTests() {
        console.log('✅ Running Validation Tests...');
        console.log('------------------------------');
        
        // Run specific validation tests
        const validationTests = [
            'test/twilio-validation.test.js',
            'test/session-manager.test.js',
            'test/audio-processor.test.js'
        ];

        for (const testFile of validationTests) {
            if (fs.existsSync(testFile)) {
                const testResult = await this.runNodeTest(testFile);
                this.processTestResult(path.basename(testFile, '.test.js'), testResult);
            } else {
                console.log(`⏭️ Skipping ${testFile} (not found)`);
                this.results.skipped++;
            }
        }
        console.log('');
    }

    async runNodeTest(testFile) {
        return new Promise((resolve) => {
            console.log(`🧪 Running ${testFile}...`);
            
            const startTime = performance.now();
            const testProcess = spawn('node', ['--test', testFile], {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: {
                    ...process.env,
                    NODE_ENV: 'test',
                    PORT: TEST_SERVER_PORT
                }
            });

            let stdout = '';
            let stderr = '';

            testProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            testProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            testProcess.on('close', (code) => {
                const endTime = performance.now();
                const duration = endTime - startTime;

                resolve({
                    code,
                    stdout,
                    stderr,
                    duration,
                    file: testFile
                });
            });

            // Timeout after 30 seconds
            setTimeout(() => {
                testProcess.kill('SIGTERM');
                resolve({
                    code: -1,
                    stdout,
                    stderr: stderr + '\nTest timed out after 30 seconds',
                    duration: 30000,
                    file: testFile
                });
            }, 30000);
        });
    }

    processTestResult(testName, result) {
        const { code, stdout, stderr, duration, file } = result;
        
        this.results.performance[testName] = duration;
        
        if (code === 0) {
            console.log(`✅ ${testName} passed (${Math.round(duration)}ms)`);
            this.results.passed++;
        } else {
            console.log(`❌ ${testName} failed (${Math.round(duration)}ms)`);
            this.results.failed++;
            this.results.errors.push({
                test: testName,
                file,
                code,
                stdout: stdout.slice(-500), // Last 500 chars
                stderr: stderr.slice(-500)  // Last 500 chars
            });
        }
        
        this.results.total++;
        
        // Show test output summary
        if (stdout.includes('✅') || stdout.includes('❌')) {
            const lines = stdout.split('\n').filter(line => 
                line.includes('✅') || line.includes('❌') || line.includes('ℹ️')
            );
            lines.slice(0, 5).forEach(line => {
                if (line.trim()) {
                    console.log(`  ${line.trim()}`);
                }
            });
        }
    }

    async generateReport() {
        console.log('📊 Generating Test Report...');
        console.log('----------------------------');
        
        const report = {
            timestamp: new Date().toISOString(),
            serverStatus: this.serverReady ? 'available' : 'unavailable',
            results: this.results,
            environment: {
                nodeVersion: process.version,
                platform: process.platform,
                serverPort: TEST_SERVER_PORT,
                serverUrl: BASE_URL
            },
            fixesValidated: {
                'Twilio Audio Streaming (Sequence Numbers)': this.results.passed > 0,
                'Standardized Instruction Sending': this.results.passed > 0,
                'Inbound Configuration Loading': this.results.passed > 0,
                'Testing Flow Consistency': this.results.passed > 0,
                'Security & Cleanup': this.results.passed > 0
            }
        };

        // Save report to file
        const reportPath = 'test-results-session-fixes.json';
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`📄 Report saved to: ${reportPath}`);
        
        // Generate markdown summary
        await this.generateMarkdownSummary(report);
    }

    async generateMarkdownSummary(report) {
        const totalDuration = this.results.endTime - this.results.startTime;
        
        const markdown = `# Session Handling Fixes - Test Results

## Summary
- **Date**: ${new Date().toISOString()}
- **Total Duration**: ${Math.round(totalDuration)}ms
- **Server Status**: ${report.serverStatus}
- **Tests Run**: ${this.results.total}
- **Passed**: ${this.results.passed} ✅
- **Failed**: ${this.results.failed} ❌
- **Skipped**: ${this.results.skipped} ⏭️

## Fixes Validated
${Object.entries(report.fixesValidated).map(([fix, validated]) => 
    `- ${validated ? '✅' : '❌'} ${fix}`
).join('\n')}

## Performance
${Object.entries(this.results.performance).map(([test, duration]) => 
    `- **${test}**: ${Math.round(duration)}ms`
).join('\n')}

## Environment
- **Node.js**: ${process.version}
- **Platform**: ${process.platform}
- **Server**: ${BASE_URL}

${this.results.errors.length > 0 ? `
## Errors
${this.results.errors.map(error => `
### ${error.test}
\`\`\`
${error.stderr || error.stdout || 'No error details'}
\`\`\`
`).join('\n')}
` : '## ✅ No Errors'}

---
*Generated by Session Handling Fixes Test Runner*
`;

        fs.writeFileSync('TEST_RESULTS_SESSION_FIXES.md', markdown);
        console.log('📄 Markdown summary saved to: TEST_RESULTS_SESSION_FIXES.md');
    }

    printSummary() {
        const totalDuration = this.results.endTime - this.results.startTime;
        
        console.log('\n🏁 TEST EXECUTION COMPLETE');
        console.log('==========================');
        console.log(`⏱️ Total Duration: ${Math.round(totalDuration)}ms`);
        console.log(`📊 Tests Run: ${this.results.total}`);
        console.log(`✅ Passed: ${this.results.passed}`);
        console.log(`❌ Failed: ${this.results.failed}`);
        console.log(`⏭️ Skipped: ${this.results.skipped}`);
        
        if (this.results.failed === 0) {
            console.log('\n🎉 ALL SESSION HANDLING FIXES VALIDATED SUCCESSFULLY!');
            console.log('✅ Twilio Audio Streaming - Sequence Numbers');
            console.log('✅ Standardized Instruction Sending');
            console.log('✅ Inbound Configuration Loading');
            console.log('✅ Testing Flow Consistency');
            console.log('✅ Security & Cleanup');
        } else {
            console.log('\n⚠️ Some tests failed - check the detailed report');
            console.log('📄 See: TEST_RESULTS_SESSION_FIXES.md');
        }
        
        console.log('\n====================================================');
        console.log(`📅 Completed at: ${new Date().toISOString()}`);
        console.log('====================================================\n');
    }
}

// Run the tests
const runner = new TestRunner();
runner.run().catch(error => {
    console.error('💥 Test runner crashed:', error);
    process.exit(1);
});
