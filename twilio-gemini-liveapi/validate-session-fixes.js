#!/usr/bin/env node

/**
 * Quick Session Fixes Validator
 * 
 * Fast validation script to check if the session handling fixes are working.
 * This script performs quick checks without full integration tests.
 */

import fs from 'fs';
import path from 'path';

const TEST_SERVER_PORT = process.env.PORT || 3101;
const BASE_URL = `http://localhost:${TEST_SERVER_PORT}`;

class SessionFixesValidator {
    constructor() {
        this.results = {
            fixes: {},
            serverAvailable: false,
            timestamp: new Date().toISOString()
        };
    }

    async validate() {
        console.log('🔍 SESSION HANDLING FIXES - QUICK VALIDATION');
        console.log('============================================');
        console.log(`📅 ${this.results.timestamp}`);
        console.log(`🌐 Server: ${BASE_URL}\n`);

        // Check server availability
        await this.checkServer();

        // Validate each fix
        await this.validateFix1_SequenceNumbers();
        await this.validateFix2_InstructionSending();
        await this.validateFix3_InboundConfig();
        await this.validateFix4_FlowConsistency();
        await this.validateFix5_Security();

        // Print summary
        this.printSummary();
    }

    async checkServer() {
        console.log('🔍 Checking server status...');
        try {
            const response = await fetch(`${BASE_URL}/health`);
            this.results.serverAvailable = response.ok;
            console.log(`${response.ok ? '✅' : '❌'} Server ${response.ok ? 'available' : 'unavailable'}`);
        } catch (error) {
            this.results.serverAvailable = false;
            console.log(`❌ Server unavailable: ${error.message}`);
        }
        console.log('');
    }

    async validateFix1_SequenceNumbers() {
        console.log('🔧 Fix 1: Twilio Audio Streaming - Sequence Numbers');
        console.log('---------------------------------------------------');
        
        try {
            // Check if the code changes are present
            const sessionManagerPath = 'src/session/session-manager.js';
            const twilioHandlerPath = 'src/websocket/twilio-flow-handler.js';
            
            let codeFixed = false;
            
            if (fs.existsSync(sessionManagerPath)) {
                const sessionManagerCode = fs.readFileSync(sessionManagerPath, 'utf8');
                const hasSequenceNumberLogic = sessionManagerCode.includes('sequenceNumber') &&
                                             sessionManagerCode.includes('connectionData.sequenceNumber++');
                
                if (hasSequenceNumberLogic) {
                    console.log('✅ Sequence number logic found in session-manager.js');
                    codeFixed = true;
                } else {
                    console.log('❌ Sequence number logic missing in session-manager.js');
                }
            }
            
            if (fs.existsSync(twilioHandlerPath)) {
                const twilioHandlerCode = fs.readFileSync(twilioHandlerPath, 'utf8');
                const hasSequenceInit = twilioHandlerCode.includes('sequenceNumber: 0');
                
                if (hasSequenceInit) {
                    console.log('✅ Sequence number initialization found in twilio-flow-handler.js');
                    codeFixed = codeFixed && true;
                } else {
                    console.log('❌ Sequence number initialization missing in twilio-flow-handler.js');
                    codeFixed = false;
                }
            }
            
            this.results.fixes.sequenceNumbers = {
                status: codeFixed ? 'implemented' : 'missing',
                details: 'Sequence number tracking for Twilio audio packets'
            };
            
        } catch (error) {
            console.log(`❌ Error validating Fix 1: ${error.message}`);
            this.results.fixes.sequenceNumbers = { status: 'error', error: error.message };
        }
        console.log('');
    }

    async validateFix2_InstructionSending() {
        console.log('🔧 Fix 2: Standardized Instruction Sending');
        console.log('------------------------------------------');
        
        try {
            const sessionManagerPath = 'src/session/session-manager.js';
            
            if (fs.existsSync(sessionManagerPath)) {
                const sessionManagerCode = fs.readFileSync(sessionManagerPath, 'utf8');
                
                // Check if the double instruction sending was removed
                const hasRemovedDoubleInstructions = sessionManagerCode.includes('REMOVED: AI instruction sending') ||
                                                   sessionManagerCode.includes('instructions will be sent by flow handler');
                
                const stillHasOldInstructionSending = sessionManagerCode.includes('await geminiSession.sendClientContent') &&
                                                    sessionManagerCode.includes('aiInstructions') &&
                                                    !sessionManagerCode.includes('REMOVED');
                
                if (hasRemovedDoubleInstructions && !stillHasOldInstructionSending) {
                    console.log('✅ Double instruction sending removed from session-manager.js');
                    this.results.fixes.instructionSending = {
                        status: 'implemented',
                        details: 'Instructions now sent only by flow handlers'
                    };
                } else {
                    console.log('❌ Double instruction sending still present in session-manager.js');
                    this.results.fixes.instructionSending = {
                        status: 'missing',
                        details: 'Session manager still sends instructions'
                    };
                }
            }
            
        } catch (error) {
            console.log(`❌ Error validating Fix 2: ${error.message}`);
            this.results.fixes.instructionSending = { status: 'error', error: error.message };
        }
        console.log('');
    }

    async validateFix3_InboundConfig() {
        console.log('🔧 Fix 3: Inbound Configuration Loading');
        console.log('---------------------------------------');
        
        try {
            const configHandlersPath = 'src/websocket/config-handlers.js';
            
            if (fs.existsSync(configHandlersPath)) {
                const configCode = fs.readFileSync(configHandlersPath, 'utf8');
                
                // Check if inbound config loading was improved
                const hasImprovedInboundConfig = configCode.includes('isInboundConfig') ||
                                               configCode.includes('scriptType === \'inbound\'') ||
                                               configCode.includes('storedConfig.isIncomingCall');
                
                if (hasImprovedInboundConfig) {
                    console.log('✅ Improved inbound configuration loading found');
                    this.results.fixes.inboundConfig = {
                        status: 'implemented',
                        details: 'Enhanced inbound config detection and loading'
                    };
                } else {
                    console.log('❌ Inbound configuration loading not improved');
                    this.results.fixes.inboundConfig = {
                        status: 'missing',
                        details: 'Config loading still uses basic logic'
                    };
                }
            }
            
            // Test campaign script availability if server is running
            if (this.results.serverAvailable) {
                try {
                    const response = await fetch(`${BASE_URL}/get-campaign-script/7`);
                    if (response.ok) {
                        console.log('✅ Inbound campaign script 7 accessible');
                    } else {
                        console.log(`⚠️ Inbound campaign script 7 not accessible (${response.status})`);
                    }
                } catch (error) {
                    console.log(`⚠️ Could not test campaign script access: ${error.message}`);
                }
            }
            
        } catch (error) {
            console.log(`❌ Error validating Fix 3: ${error.message}`);
            this.results.fixes.inboundConfig = { status: 'error', error: error.message };
        }
        console.log('');
    }

    async validateFix4_FlowConsistency() {
        console.log('🔧 Fix 4: Testing Flow Consistency');
        console.log('----------------------------------');
        
        try {
            const localTestingPath = 'src/websocket/local-testing-handler.js';
            const twilioFlowPath = 'src/websocket/twilio-flow-handler.js';
            
            let consistencyFixed = false;
            
            if (fs.existsSync(localTestingPath)) {
                const localTestingCode = fs.readFileSync(localTestingPath, 'utf8');
                
                // Check for proper inbound/outbound triggers
                const hasProperTriggers = localTestingCode.includes('A customer has just called you') &&
                                        localTestingCode.includes('The call has been answered');
                
                if (hasProperTriggers) {
                    console.log('✅ Proper inbound/outbound triggers found in local-testing-handler.js');
                    consistencyFixed = true;
                } else {
                    console.log('❌ Proper triggers missing in local-testing-handler.js');
                }
            }
            
            if (fs.existsSync(twilioFlowPath)) {
                const twilioFlowCode = fs.readFileSync(twilioFlowPath, 'utf8');
                
                // Check for consistent triggers in production
                const hasConsistentTriggers = twilioFlowCode.includes('A customer has just called you') &&
                                            twilioFlowCode.includes('The call has been answered');
                
                if (hasConsistentTriggers) {
                    console.log('✅ Consistent triggers found in twilio-flow-handler.js');
                    consistencyFixed = consistencyFixed && true;
                } else {
                    console.log('❌ Inconsistent triggers in twilio-flow-handler.js');
                    consistencyFixed = false;
                }
            }
            
            this.results.fixes.flowConsistency = {
                status: consistencyFixed ? 'implemented' : 'missing',
                details: 'Proper inbound/outbound triggers for testing and production'
            };
            
        } catch (error) {
            console.log(`❌ Error validating Fix 4: ${error.message}`);
            this.results.fixes.flowConsistency = { status: 'error', error: error.message };
        }
        console.log('');
    }

    async validateFix5_Security() {
        console.log('🔧 Fix 5: Security & Cleanup');
        console.log('----------------------------');
        
        try {
            const routesPath = 'src/api/routes.js';
            
            if (fs.existsSync(routesPath)) {
                const routesCode = fs.readFileSync(routesPath, 'utf8');
                
                // Check if webhook validation was re-enabled
                const hasEnabledValidation = !routesCode.includes('Temporarily disable webhook validation') &&
                                           !routesCode.includes('TODO: Re-enable after fixing') &&
                                           routesCode.includes('validateTwilioWebhook(request)');
                
                if (hasEnabledValidation) {
                    console.log('✅ Twilio webhook validation re-enabled');
                    this.results.fixes.security = {
                        status: 'implemented',
                        details: 'Webhook signature validation active'
                    };
                } else {
                    console.log('❌ Webhook validation still disabled');
                    this.results.fixes.security = {
                        status: 'missing',
                        details: 'Webhook validation commented out'
                    };
                }
            }
            
            // Test webhook security if server is running
            if (this.results.serverAvailable) {
                try {
                    const response = await fetch(`${BASE_URL}/incoming-call`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: 'CallSid=CAtest123'
                    });
                    
                    if (response.status === 403) {
                        console.log('✅ Webhook security validation working (403 for invalid signature)');
                    } else {
                        console.log(`⚠️ Webhook returned ${response.status} (expected 403 for invalid signature)`);
                    }
                } catch (error) {
                    console.log(`⚠️ Could not test webhook security: ${error.message}`);
                }
            }
            
        } catch (error) {
            console.log(`❌ Error validating Fix 5: ${error.message}`);
            this.results.fixes.security = { status: 'error', error: error.message };
        }
        console.log('');
    }

    printSummary() {
        console.log('📊 VALIDATION SUMMARY');
        console.log('====================');
        
        const fixes = Object.entries(this.results.fixes);
        const implemented = fixes.filter(([_, fix]) => fix.status === 'implemented').length;
        const total = fixes.length;
        
        console.log(`✅ Implemented: ${implemented}/${total}`);
        console.log(`🌐 Server: ${this.results.serverAvailable ? 'Available' : 'Unavailable'}`);
        console.log('');
        
        fixes.forEach(([name, fix]) => {
            const icon = fix.status === 'implemented' ? '✅' : 
                        fix.status === 'missing' ? '❌' : '⚠️';
            console.log(`${icon} ${name}: ${fix.status}`);
            if (fix.details) {
                console.log(`   ${fix.details}`);
            }
        });
        
        console.log('');
        
        if (implemented === total) {
            console.log('🎉 ALL SESSION HANDLING FIXES VALIDATED!');
            console.log('Ready to run comprehensive tests with: npm run test:session-fixes');
        } else {
            console.log('⚠️ Some fixes need attention. Check the details above.');
        }
        
        console.log('\n====================');
        console.log(`📅 Completed: ${new Date().toISOString()}`);
        console.log('====================');
        
        // Save results
        fs.writeFileSync('validation-results.json', JSON.stringify(this.results, null, 2));
        console.log('📄 Results saved to: validation-results.json\n');
    }
}

// Run validation
const validator = new SessionFixesValidator();
validator.validate().catch(error => {
    console.error('💥 Validation failed:', error);
    process.exit(1);
});
