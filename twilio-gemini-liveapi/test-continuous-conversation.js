#!/usr/bin/env node

/**
 * Test script to verify continuous conversation implementation
 * Tests the Live API setup configuration and continuous conversation flow
 */

import pkg from '@google/genai';
const { GoogleGenAI } = pkg;
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

if (!GEMINI_API_KEY) {
    console.error('❌ GEMINI_API_KEY not found in environment variables');
    process.exit(1);
}

async function testContinuousConversation() {
    console.log('🧪 Testing Continuous Conversation Implementation...');
    console.log('🔧 Using Live API setup configuration (not sendClientContent)');
    
    try {
        const geminiClient = new GoogleGenAI(GEMINI_API_KEY);
        
        let setupComplete = false;
        let responseReceived = false;
        let sessionRef = null;

        console.log('📞 Creating Live API session...');

        sessionRef = await geminiClient.live.connect({
            model: 'gemini-2.5-flash-preview-native-audio-dialog',
            callbacks: {
                onopen: async () => {
                    console.log('✅ Session opened successfully');

                    try {
                        console.log('🔧 Sending Live API setup configuration...');

                        // Use the CORRECT Live API pattern (not sendClientContent)
                        await sessionRef.send({
                            setup: {
                                model: 'gemini-2.5-flash-preview-native-audio-dialog',
                                systemInstruction: {
                                    parts: [{
                                        text: 'You are a helpful assistant. When I say hello, respond with "Hello! I am working correctly with continuous conversation enabled. You can speak to me multiple times and I will respond to each message." Keep your responses brief and confirm that continuous conversation is working.'
                                    }]
                                },
                                voice: {
                                    name: 'Kore'
                                },
                                generationConfig: {
                                    responseModalities: ['AUDIO'],
                                    speechConfig: {
                                        voiceConfig: {
                                            prebuiltVoiceConfig: {
                                                voiceName: 'Kore'
                                            }
                                        }
                                    }
                                }
                            }
                        });
                        
                        console.log('✅ Live API setup configuration sent successfully');
                        setupComplete = true;
                        
                    } catch (setupError) {
                        console.error('❌ Error sending setup configuration:', setupError);
                        process.exit(1);
                    }
                },
                onmessage: (message) => {
                    console.log('📨 Received message from Gemini:', JSON.stringify(message, null, 2));
                    
                    if (message.setupComplete) {
                        console.log('✅ Setup completed - session ready for continuous conversation');
                        
                        // Test sending a simple text message after setup
                        setTimeout(async () => {
                            console.log('💬 Sending test message...');
                            try {
                                // For testing, we can send a simple text message
                                await sessionRef.send({
                                    clientContent: {
                                        turns: [{
                                            role: 'user',
                                            parts: [{
                                                text: 'Hello, please confirm continuous conversation is working'
                                            }]
                                        }]
                                    }
                                });
                                console.log('✅ Test message sent');
                            } catch (error) {
                                console.error('❌ Error sending test message:', error);
                            }
                        }, 1000);
                    }
                    
                    if (message.serverContent?.modelTurn?.parts?.[0]?.text) {
                        const response = message.serverContent.modelTurn.parts[0].text;
                        console.log('🤖 Gemini text response:', response);
                        responseReceived = true;
                        
                        // Test sending a second message to verify continuous conversation
                        setTimeout(async () => {
                            console.log('💬 Sending second test message to verify continuous conversation...');
                            try {
                                await sessionRef.send({
                                    clientContent: {
                                        turns: [{
                                            role: 'user',
                                            parts: [{
                                                text: 'Great! Now please respond to this second message to prove continuous conversation works.'
                                            }]
                                        }]
                                    }
                                });
                                console.log('✅ Second test message sent');
                            } catch (error) {
                                console.error('❌ Error sending second test message:', error);
                            }
                        }, 2000);
                    }
                    
                    if (message.serverContent?.modelTurn?.parts?.[0]?.inlineData) {
                        console.log('🔊 Received audio response from Gemini');
                        responseReceived = true;
                    }
                },
                onerror: (error) => {
                    console.error('❌ Session error:', error);
                    process.exit(1);
                },
                onclose: () => {
                    console.log('🔌 Session closed');
                }
            }
        });

        // Wait for test completion
        setTimeout(() => {
            if (setupComplete && responseReceived) {
                console.log('\n✅ SUCCESS! Continuous conversation implementation is working correctly');
                console.log('✅ Live API setup configuration sent successfully');
                console.log('✅ Gemini responded to messages');
                console.log('✅ Session supports continuous conversation flow');
            } else {
                console.log('\n❌ FAILURE! Issues detected:');
                if (!setupComplete) console.log('❌ Setup configuration failed');
                if (!responseReceived) console.log('❌ No response received from Gemini');
            }
            
            sessionRef.close();
            process.exit(setupComplete && responseReceived ? 0 : 1);
        }, 10000);
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}

// Run the test
testContinuousConversation();
