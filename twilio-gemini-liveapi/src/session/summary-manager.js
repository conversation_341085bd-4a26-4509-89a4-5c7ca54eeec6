// Session Summary Manager - handles session summary generation and storage
import { writeFile, readFile, access, mkdir } from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class SessionSummaryManager {
    constructor() {
        this.summaryTimeouts = new Map(); // Track summary timeouts
        this.summaryInProgress = new Set(); // Track sessions currently generating summaries
        this.defaultSummaryTimeout = 15000; // 15 seconds timeout for summary generation

        // Summary prompts - use environment variable only, no hardcoded fallbacks
        this.summaryPrompts = {
            outbound_call: process.env.SUMMARY_GENERATION_PROMPT || '',
            inbound_call: process.env.SUMMARY_GENERATION_PROMPT || '',
            outbound_test: process.env.SUMMARY_GENERATION_PROMPT || '',
            inbound_test: process.env.SUMMARY_GENERATION_PROMPT || '',
            default: process.env.SUMMARY_GENERATION_PROMPT || ''
        };
    }

    /**
     * Request session summary from AI with flow-specific prompts
     * @param {string} callSid - Call/session ID
     * @param {Object} connectionData - Connection data with Gemini session
     * @param {Object} contextManager - Context manager for saving summary state
     * @returns {Promise<boolean>} - Success status
     */
    async requestSummary(callSid, connectionData, contextManager) {
        if (!connectionData || connectionData.summaryRequested || connectionData.summaryReceived) {
            console.log(`⏭️ [${callSid}] Summary request skipped (already requested/received or no connection data)`);
            return false;
        }

        if (this.summaryInProgress.has(callSid)) {
            console.log(`⏳ [${callSid}] Summary generation already in progress`);
            return false;
        }

        // Determine flow type for appropriate summary prompt
        const flowType = this.determineFlowType(connectionData);
        const summaryPrompt = this.getSummaryPrompt(flowType);

        console.log(`📝 [${callSid}] Requesting ${flowType} session summary from AI`);
        connectionData.summaryRequested = true;
        connectionData.summaryText = '';
        connectionData.summaryFlowType = flowType;
        this.summaryInProgress.add(callSid);

        try {
            // Check if we have a valid Gemini session
            if (!connectionData.geminiSession) {
                console.log(`❌ [${callSid}] No active Gemini session for summary request`);
                return await this.generateFallbackSummary(callSid, connectionData, contextManager);
            }

            // Set timeout for summary generation
            const timeoutId = setTimeout(async () => {
                console.log(`⏰ [${callSid}] Summary generation timeout - using fallback`);
                await this.handleSummaryTimeout(callSid, connectionData, contextManager);
            }, this.defaultSummaryTimeout);

            connectionData.summaryTimeoutId = timeoutId;
            this.summaryTimeouts.set(callSid, timeoutId);

            // Send flow-specific summary request to AI using Live API
            await connectionData.geminiSession.send({
                clientContent: {
                    turns: [{
                        role: 'user',
                        parts: [{
                            text: summaryPrompt
                        }]
                    }]
                }
            });

            console.log(`✅ [${callSid}] ${flowType} summary request sent to AI successfully`);
            return true;

        } catch (error) {
            console.error(`❌ [${callSid}] Error requesting summary from AI:`, error);
            this.summaryInProgress.delete(callSid);
            return await this.generateFallbackSummary(callSid, connectionData, contextManager);
        }
    }

    /**
     * Determine the flow type from connection data
     * @param {Object} connectionData - Connection data
     * @returns {string} - Flow type
     */
    determineFlowType(connectionData) {
        if (connectionData.flowType) {
            return connectionData.flowType;
        }

        if (connectionData.sessionType === 'local_test') {
            return connectionData.isIncomingCall ? 'inbound_test' : 'outbound_test';
        }

        if (connectionData.sessionType === 'twilio') {
            return connectionData.isIncomingCall ? 'inbound_call' : 'outbound_call';
        }

        // Fallback determination
        if (connectionData.isTestMode) {
            return connectionData.isIncomingCall ? 'inbound_test' : 'outbound_test';
        }

        return connectionData.isIncomingCall ? 'inbound_call' : 'outbound_call';
    }

    /**
     * Get appropriate summary prompt for flow type
     * @param {string} flowType - Flow type
     * @returns {string} - Summary prompt
     */
    getSummaryPrompt(flowType) {
        return this.summaryPrompts[flowType] || this.summaryPrompts.default;
    }

    /**
     * Handle summary response from AI
     * @param {string} callSid - Call/session ID
     * @param {string} summaryText - Summary text from AI
     * @param {Object} connectionData - Connection data
     * @param {Object} contextManager - Context manager
     */
    async handleSummaryResponse(callSid, summaryText, connectionData, contextManager) {
        if (!this.summaryInProgress.has(callSid)) {
            console.log(`⚠️ [${callSid}] Received summary but no generation in progress`);
            return;
        }

        console.log(`📋 [${callSid}] Received summary from AI (${summaryText.length} characters)`);
        
        // Clear timeout
        this.clearSummaryTimeout(callSid, connectionData);

        // Save summary
        await this.saveSummaryInfo(callSid, summaryText, 'neutral', 'completed', 
            connectionData.targetName, connectionData.targetPhoneNumber);

        // Mark as received
        connectionData.summaryReceived = true;
        connectionData.summaryText = summaryText;
        this.summaryInProgress.delete(callSid);

        // Update context
        if (contextManager) {
            const context = contextManager.getSessionContext(callSid);
            if (context) {
                context.conversationState.summaryReceived = true;
                context.conversationState.summaryText = summaryText;
                contextManager.contextStore.set(callSid, context);
            }
        }

        console.log(`✅ [${callSid}] Summary processed and saved successfully`);
    }

    /**
     * Generate fallback summary from conversation data with flow-specific formatting
     * @param {string} callSid - Call/session ID
     * @param {Object} connectionData - Connection data
     * @param {Object} contextManager - Context manager
     * @returns {Promise<boolean>} - Success status
     */
    async generateFallbackSummary(callSid, connectionData, contextManager) {
        console.log(`📋 [${callSid}] Generating fallback summary from conversation data`);

        try {
            const flowType = this.determineFlowType(connectionData);
            let summary = "";
            let sentiment = "neutral";

            // Flow-specific summary prefixes
            const summaryPrefixes = {
                outbound_call: "Outbound call completed",
                inbound_call: "Inbound customer call completed",
                outbound_test: "Outbound call testing session completed",
                inbound_test: "Inbound call testing session completed",
                default: "Session completed"
            };

            const prefix = summaryPrefixes[flowType] || summaryPrefixes.default;
            summary = `${prefix} with ${connectionData.targetName || 'caller'}. `;

            // Use conversation log if available
            if (connectionData.conversationLog && connectionData.conversationLog.length > 0) {
                console.log(`📊 [${callSid}] Generating ${flowType} summary from ${connectionData.conversationLog.length} conversation entries`);

                // Extract key information from conversation
                const userMessages = connectionData.conversationLog.filter(entry => entry.role === 'user').map(entry => entry.content);
                const aiMessages = connectionData.conversationLog.filter(entry => entry.role === 'assistant').map(entry => entry.content);

                // Create a basic summary from the conversation
                const conversationSummary = [];
                if (userMessages.length > 0) {
                    const userLabel = flowType.includes('inbound') ? 'Customer' : 'Contact';
                    conversationSummary.push(`${userLabel} spoke ${userMessages.length} times`);
                }
                if (aiMessages.length > 0) {
                    conversationSummary.push(`AI responded ${aiMessages.length} times`);
                }

                // Try to extract key topics or outcomes
                const allText = [...userMessages, ...aiMessages].join(' ').toLowerCase();
                const keyTopics = [];

                // Flow-specific keyword detection
                if (flowType.includes('inbound')) {
                    // Inbound call topics
                    if (allText.includes('support') || allText.includes('help')) keyTopics.push('customer support');
                    if (allText.includes('complaint') || allText.includes('problem')) keyTopics.push('complaint resolution');
                    if (allText.includes('order') || allText.includes('purchase')) keyTopics.push('order inquiry');
                    if (allText.includes('billing') || allText.includes('payment')) keyTopics.push('billing support');
                    if (allText.includes('technical') || allText.includes('issue')) keyTopics.push('technical support');
                } else {
                    // Outbound call topics
                    if (allText.includes('sale') || allText.includes('offer')) keyTopics.push('sales pitch');
                    if (allText.includes('follow') || allText.includes('up')) keyTopics.push('follow-up');
                    if (allText.includes('survey') || allText.includes('feedback')) keyTopics.push('survey/feedback');
                    if (allText.includes('appointment') || allText.includes('meeting')) keyTopics.push('appointment setting');
                    if (allText.includes('information') || allText.includes('update')) keyTopics.push('information sharing');
                }

                // Enhanced sentiment analysis
                const positiveWords = ['thank', 'great', 'good', 'excellent', 'satisfied', 'happy', 'yes', 'agree'];
                const negativeWords = ['problem', 'issue', 'bad', 'terrible', 'unsatisfied', 'no', 'disagree', 'complaint'];

                const positiveCount = positiveWords.filter(word => allText.includes(word)).length;
                const negativeCount = negativeWords.filter(word => allText.includes(word)).length;

                if (positiveCount > negativeCount) {
                    sentiment = 'positive';
                } else if (negativeCount > positiveCount) {
                    sentiment = 'negative';
                }

                // Build flow-specific summary
                if (conversationSummary.length > 0) {
                    summary += conversationSummary.join(', ') + '. ';
                }
                if (keyTopics.length > 0) {
                    summary += `Topics: ${keyTopics.join(', ')}. `;
                }

                // Add outcome based on flow type
                if (flowType.includes('test')) {
                    summary += 'Testing session completed successfully. ';
                } else if (flowType.includes('outbound')) {
                    summary += sentiment === 'positive' ? 'Call outcome: Positive engagement. ' :
                              sentiment === 'negative' ? 'Call outcome: Resistance encountered. ' :
                              'Call outcome: Neutral response. ';
                } else {
                    summary += sentiment === 'positive' ? 'Customer satisfaction: High. ' :
                              sentiment === 'negative' ? 'Customer satisfaction: Low, requires follow-up. ' :
                              'Customer satisfaction: Neutral. ';
                }

                // Add first few exchanges for context
                if (connectionData.conversationLog.length > 0) {
                    const firstExchange = connectionData.conversationLog.slice(0, 2);
                    const exchangeText = firstExchange.map(entry =>
                        `${entry.role}: ${entry.content.substring(0, 80)}`
                    ).join(' | ');
                    summary += `Initial exchange: ${exchangeText}`;
                }

            } else if (connectionData.fullTranscript && connectionData.fullTranscript.length > 0) {
                console.log(`📊 [${callSid}] Using full transcript for ${flowType} summary (${connectionData.fullTranscript.length} entries)`);
                summary += `Transcript available with ${connectionData.fullTranscript.length} entries.`;
            } else {
                console.log(`📊 [${callSid}] No conversation data available for ${flowType}, using basic summary`);
                summary += 'No detailed conversation data available.';
            }

            // Add session duration if available
            if (connectionData.sessionStartTime) {
                const duration = Math.round((Date.now() - connectionData.sessionStartTime) / 1000);
                summary += ` Duration: ${duration} seconds.`;
            }

            // Save the summary with flow type information
            await this.saveSummaryInfo(callSid, summary, sentiment, "completed",
                connectionData.targetName, connectionData.targetPhoneNumber, flowType);

            connectionData.summaryReceived = true;
            connectionData.summaryText = summary;
            connectionData.summaryFlowType = flowType;
            this.summaryInProgress.delete(callSid);

            console.log(`✅ [${callSid}] ${flowType} fallback summary generated and saved successfully`);
            return true;

        } catch (error) {
            console.error(`❌ [${callSid}] Error generating fallback summary:`, error);
            this.summaryInProgress.delete(callSid);
            return false;
        }
    }

    /**
     * Handle summary timeout
     * @param {string} callSid - Call/session ID
     * @param {Object} connectionData - Connection data
     * @param {Object} contextManager - Context manager
     */
    async handleSummaryTimeout(callSid, connectionData, contextManager) {
        console.log(`⏰ [${callSid}] Summary generation timed out`);
        
        this.clearSummaryTimeout(callSid, connectionData);
        
        if (connectionData.summaryText && connectionData.summaryText.length > 0) {
            // Use partial summary if available
            console.log(`📝 [${callSid}] Using partial summary received before timeout`);
            await this.saveSummaryInfo(callSid, connectionData.summaryText, 'neutral', 'timeout',
                connectionData.targetName, connectionData.targetPhoneNumber);
        } else {
            // Generate fallback summary
            await this.generateFallbackSummary(callSid, connectionData, contextManager);
        }

        connectionData.summaryReceived = true;
        this.summaryInProgress.delete(callSid);
    }

    /**
     * Clear summary timeout
     * @param {string} callSid - Call/session ID
     * @param {Object} connectionData - Connection data
     */
    clearSummaryTimeout(callSid, connectionData) {
        const timeoutId = this.summaryTimeouts.get(callSid);
        if (timeoutId) {
            clearTimeout(timeoutId);
            this.summaryTimeouts.delete(callSid);
        }
        
        if (connectionData && connectionData.summaryTimeoutId) {
            clearTimeout(connectionData.summaryTimeoutId);
            connectionData.summaryTimeoutId = null;
        }
    }

    /**
     * Save summary information to file with flow type support
     * @param {string} callSid - Call/session ID
     * @param {string} rawSummaryText - Raw summary text
     * @param {string} defaultSentiment - Default sentiment
     * @param {string} status - Call status
     * @param {string} targetName - Target name
     * @param {string} targetPhoneNumber - Target phone number
     * @param {string} flowType - Flow type (outbound_call, inbound_call, etc.)
     */
    async saveSummaryInfo(callSid, rawSummaryText, defaultSentiment = "neutral", status = "completed", targetName = null, targetPhoneNumber = null, flowType = null) {
        const dataDir = path.join(process.cwd(), 'data');
        const infoFilePath = path.join(dataDir, `${callSid}_info.json`);
        
        console.log(`💾 [${callSid}] Saving summary info. Status: ${status}, Target: ${targetName}`);
        console.log(`📋 [${callSid}] Summary text: "${rawSummaryText?.substring(0, 100)}..." (length: ${rawSummaryText?.length || 0})`);

        let summary = "Summary generation failed or was not applicable.";
        let sentiment = defaultSentiment;

        // Parse summary and sentiment
        if (rawSummaryText && status !== 'incomplete' && status !== 'error' && !['failed', 'canceled', 'no-answer', 'busy'].includes(status)) {
            const sentimentMatch = rawSummaryText.match(/sentiment:\s*(positive|neutral|negative)/i);
            if (sentimentMatch && sentimentMatch[1]) {
                sentiment = sentimentMatch[1].toLowerCase();
                summary = rawSummaryText.replace(/sentiment:\s*(positive|neutral|negative)/i, '').trim();
                console.log(`🎭 [${callSid}] Parsed sentiment: ${sentiment}`);
            } else {
                summary = rawSummaryText.trim();
            }
        } else if (rawSummaryText) {
            summary = rawSummaryText;
        } else if (['failed', 'canceled', 'no-answer', 'busy'].includes(status)) {
            summary = `Call ended with status: ${status}. No summary generated.`;
            sentiment = 'neutral';
        } else if (status === 'timeout') {
            summary = "Summary generation timed out.";
            sentiment = 'neutral';
        } else if (status === 'completed' && targetName) {
            summary = `Call completed with ${targetName}. AI summary generation was not successful, but the call connected and ended normally.`;
            sentiment = 'neutral';
        }

        const summaryData = {
            callSid: callSid,
            call_summary: summary,
            customer_sentiment: sentiment,
            status: status,
            targetName: targetName || "Unknown",
            targetPhoneNumber: targetPhoneNumber || "Unknown",
            timestamp: new Date().toISOString()
        };

        try {
            // Ensure data directory exists
            await mkdir(dataDir, { recursive: true });

            // Read existing data if available
            let existingData = {};
            try {
                const existingContent = await readFile(infoFilePath, 'utf8');
                existingData = JSON.parse(existingContent);
            } catch (readError) {
                if (readError.code !== 'ENOENT') throw readError;
            }

            // Merge new data with existing data (preserve recordingUrl, etc.)
            const finalData = { ...existingData, ...summaryData };

            await writeFile(infoFilePath, JSON.stringify(finalData, null, 2));
            console.log(`✅ [${callSid}] Summary info saved to ${infoFilePath}`);
        } catch (error) {
            console.error(`❌ [${callSid}] Error saving summary info:`, error);
        }
    }

    /**
     * Check if summary is in progress
     * @param {string} callSid - Call/session ID
     * @returns {boolean} - True if summary is in progress
     */
    isSummaryInProgress(callSid) {
        return this.summaryInProgress.has(callSid);
    }

    /**
     * Get summary status
     * @param {string} callSid - Call/session ID
     * @returns {Object} - Summary status
     */
    getSummaryStatus(callSid) {
        return {
            inProgress: this.summaryInProgress.has(callSid),
            hasTimeout: this.summaryTimeouts.has(callSid),
            timestamp: Date.now()
        };
    }

    /**
     * Clean up summary tracking for a session
     * @param {string} callSid - Call/session ID
     * @param {Object} connectionData - Connection data
     */
    cleanupSummary(callSid, connectionData) {
        this.clearSummaryTimeout(callSid, connectionData);
        this.summaryInProgress.delete(callSid);
        console.log(`🧹 [${callSid}] Summary tracking cleaned up`);
    }

    /**
     * Clean up all summary resources (for shutdown)
     */
    cleanup() {
        const timeoutCount = this.summaryTimeouts.size;
        const progressCount = this.summaryInProgress.size;

        // Clear all summary timeouts
        for (const [callSid, timeout] of this.summaryTimeouts.entries()) {
            clearTimeout(timeout);
        }

        this.summaryTimeouts.clear();
        this.summaryInProgress.clear();

        console.log(`🧹 SummaryManager: Cleared ${timeoutCount} timeouts and ${progressCount} in-progress summaries`);
    }
}
