import { getConfigValue } from '../config/config.js';

// 1. OUTBOUND CALL Configuration
export function getOutboundCallConfig(deps) {
    // For outbound calls, the config should already be set by the /make-call endpoint
    // Check if there's a stored config from the webhook
    if (deps.getNextCallConfig) {
        const storedConfig = deps.getNextCallConfig();
        if (storedConfig && storedConfig.aiInstructions) {
            console.log(`✅ [OUTBOUND] Using stored config from make-call with script: ${storedConfig.scriptId}`);
            return storedConfig;
        }
    }

    // Fallback: try to get current outbound script
    try {
        const currentScript = deps.scriptManager.getCurrentOutboundScript();
        if (currentScript) {
            const config = deps.scriptManager.getScriptConfig(currentScript.id, false);
            console.log(`✅ [OUTBOUND] Using current outbound script: ${currentScript.id}`);
            return config;
        }
    } catch (error) {
        console.warn('⚠️ Error getting outbound script config:', error);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(deps.GEMINI_DEFAULT_VOICE);
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // Last resort: default outbound config
    console.warn('⚠️ [OUTBOUND] Using fallback config - no script found');
    return {
        aiInstructions: '', // Campaign script should provide all instructions
        voice: validVoice,
        model: validModel,
        targetName: null,
        targetPhoneNumber: null,
        scriptType: 'outbound',
        scriptId: 'default'
    };
}

// 2. INBOUND CALL Configuration
export function getInboundCallConfig(deps) {
    // CRITICAL FIX: Simplified logic - always check stored config first (same as outbound pattern)
    if (deps.getNextCallConfig) {
        const storedConfig = deps.getNextCallConfig();
        if (storedConfig && storedConfig.aiInstructions) {
            console.log(`✅ [INBOUND] Using stored config from webhook with script: ${storedConfig.scriptId || 'unknown'}`);
            // Ensure it's marked as inbound call
            storedConfig.isIncomingCall = true;
            storedConfig.scriptType = storedConfig.scriptType || 'inbound';
            return storedConfig;
        }
    }

    // Fallback: Try to get current incoming script
    try {
        const currentScript = deps.scriptManager.getCurrentIncomingScript();
        if (currentScript) {
            const scriptConfig = deps.scriptManager.getScriptConfig(currentScript.id, true);
            if (scriptConfig && scriptConfig.aiInstructions) {
                console.log(`✅ [INBOUND] Using current incoming script: ${currentScript.id}`);
                return scriptConfig;
            }
        }
    } catch (error) {
        console.warn('⚠️ Error getting inbound script config:', error);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice('Kore'); // Default voice for inbound
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // Last resort: Try to load a default incoming campaign script
    try {
        // Load campaign script 7 (incoming campaign 1) as default
        const defaultScript = deps.scriptManager.getScriptConfig(7, true); // ID 7 = incoming campaign 1
        if (defaultScript && defaultScript.aiInstructions) {
            console.log(`✅ [INBOUND] Using default incoming campaign script 1`);
            return defaultScript;
        }
    } catch (scriptError) {
        console.error('❌ Error loading default incoming campaign script:', scriptError);
    }

    // Final fallback to minimal config (should not happen in production)
    console.warn('⚠️ [INBOUND] Using minimal fallback config - this should not happen in production!');
    return {
        aiInstructions: 'You are a helpful customer service representative. Greet the caller warmly and ask how you can help them today.',
        voice: validVoice,
        model: validModel,
        targetName: null,
        targetPhoneNumber: null,
        scriptType: 'inbound',
        scriptId: 'customer-service'
    };
}

// 3. OUTBOUND TESTING Configuration
export function getOutboundTestConfig(deps) {
    try {
        console.log('🔍 DEBUG: Getting current outbound script...');
        const currentScript = deps.scriptManager.getCurrentOutboundScript();
        console.log('🔍 DEBUG: Current outbound script:', !!currentScript, currentScript?.id);
        if (currentScript) {
            console.log('🔍 DEBUG: Getting script config for outbound ID:', currentScript.id);
            const config = deps.scriptManager.getScriptConfig(currentScript.id, false);
            console.log('🔍 DEBUG: Outbound script config result:', !!config, config?.aiInstructions?.length);
            if (config && config.aiInstructions) {
                console.log(`✅ [OUTBOUND TEST] Using real campaign script: ${config.aiInstructions.substring(0, 100)}...`);
                return {
                    ...config,
                    isTestMode: true
                };
            }
        }
    } catch (error) {
        console.warn('⚠️ Error getting outbound test config:', error);
    }

    // CRITICAL FIX: Load real campaign script instead of generic instructions
    console.log(`🔧 [OUTBOUND TEST] Loading real campaign script as fallback...`);

    try {
        // Load campaign script 1 (outbound) as default for testing
        const scriptStartTime = Date.now();
        const campaignScript = deps.scriptManager.getScriptConfig(1, false); // ID 1 = outbound campaign 1
        const scriptLoadTime = Date.now() - scriptStartTime;
        console.log(`⏱️ [OUTBOUND TEST] Script loading took ${scriptLoadTime}ms`);
        
        if (campaignScript && campaignScript.aiInstructions) {
            console.log(`✅ [OUTBOUND TEST] Using real campaign script 1: ${campaignScript.aiInstructions.substring(0, 100)}...`);
            return {
                ...campaignScript,
                isTestMode: true
            };
        }
    } catch (scriptError) {
        console.error('❌ Error loading outbound campaign script:', scriptError);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(deps.GEMINI_DEFAULT_VOICE);
    const validModel = deps.modelManager.getValidGeminiModel(deps.GEMINI_DEFAULT_MODEL);

    // DEBUG: Check what the model manager is actually returning
    console.log(`🔍 DEBUG: deps.GEMINI_DEFAULT_MODEL = "${deps.GEMINI_DEFAULT_MODEL}"`);
    console.log(`🔍 DEBUG: validModel from manager = "${validModel}"`);
    console.log(`🔍 DEBUG: modelManager.getDefaultModel() = "${deps.modelManager.getDefaultModel()}"`);
    console.log(`🔍 DEBUG: process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);

    // CRITICAL ERROR: No campaign script available - this should not happen
    console.error('❌ [OUTBOUND TEST] CRITICAL: No campaign script could be loaded! Testing requires valid scripts.');
    throw new Error('No outbound campaign script available for testing. Please ensure campaign scripts are properly configured.');
}

// 4. INBOUND TESTING Configuration
export function getInboundTestConfig(deps) {
    try {
        console.log(`🔍 DEBUG: Getting current incoming script...`);
        const currentScript = deps.scriptManager.getCurrentIncomingScript();
        console.log(`🔍 DEBUG: Current script:`, !!currentScript, currentScript?.id);
        if (currentScript) {
            console.log(`🔍 DEBUG: Getting script config for ID: ${currentScript.id}`);
            const config = deps.scriptManager.getScriptConfig(currentScript.id, true);
            console.log(`🔍 DEBUG: Script config result:`, !!config);
            if (config && config.aiInstructions) {
                console.log(`✅ [INBOUND TEST] Using real campaign script: ${config.aiInstructions.substring(0, 100)}...`);
                return {
                    ...config,
                    isTestMode: true
                };
            }
        }
    } catch (error) {
        console.warn('⚠️ Error getting inbound test config:', error);
    }

    // CRITICAL FIX: Load real campaign script instead of empty instructions
    console.log(`🔧 [INBOUND TEST] Loading real campaign script as fallback...`);

    try {
        // Load campaign script 1 (incoming) as default for testing
        const campaignScript = deps.scriptManager.getScriptConfig(7, true); // ID 7 = incoming campaign 1
        if (campaignScript && campaignScript.aiInstructions) {
            console.log(`✅ [INBOUND TEST] Using real campaign script 1: ${campaignScript.aiInstructions.substring(0, 100)}...`);
            return {
                ...campaignScript,
                isTestMode: true
            };
        }
    } catch (scriptError) {
        console.error('❌ Error loading campaign script:', scriptError);
    }

    // Use voice and model managers for validation
    const validVoice = deps.voiceManager.getValidGeminiVoice(getConfigValue('ai.gemini.defaultVoice', 'Kore'));
    const validModel = deps.modelManager.getValidGeminiModel(getConfigValue('ai.gemini.defaultModel'));

    // CRITICAL ERROR: No campaign script available - this should not happen
    console.error('❌ [INBOUND TEST] CRITICAL: No campaign script could be loaded! Testing requires valid scripts.');
    throw new Error('No inbound campaign script available for testing. Please ensure campaign scripts are properly configured.');
}