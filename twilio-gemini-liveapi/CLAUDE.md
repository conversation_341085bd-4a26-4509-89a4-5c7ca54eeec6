# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Node.js voice AI call center system that integrates Twilio for telephony, Google Gemini Live API for conversational AI, and Supabase for data storage. The project uses ES modules and includes a Next.js frontend.

**Key Principles (from FINAL_STATE.md):**
- **CAMPAIGN SCRIPTS ONLY** - 100% campaign scripts, 0% system prompts according to CAMPAIGN_SCRIPT_POLICY.md
- **Policy-based AI instructions** - All AI behavior derived from campaign scripts, not system prompts
- **4 Flows Supported**: Outbound Twilio, Outbound Testing (browser), Inbound Twilio, Inbound Testing (browser)

## Essential Commands

```bash
# Development
npm run dev          # Start with auto-reload
npm run debug        # Start with Node.js inspector

# Code Quality (ALWAYS run before completing tasks)
npm run lint         # Check code style
npm run lint:fix     # Auto-fix linting issues
npm test            # Run tests

# Production (PM2)
npm start           # Start the server
pm2 start ecosystem.config.js --only twilio-gemini-backend
pm2 logs twilio-gemini-backend

# Health & Monitoring
npm run health      # Check server health
npm run monitor     # Run dev + health monitoring
npm run audio-quality # Check audio quality

# Maintenance
npm run clean       # Clean all generated files
npm run clean:audio # Clean audio debug files
```

## Architecture

The application follows a modular architecture with clear separation of concerns:

- **Entry Point**: `index.js` - Fastify server setup with WebSocket support
- **API Layer**: `src/api/` - REST endpoints for management and testing
- **Core Services**:
  - `src/session/session-manager.js` - Central session orchestration
  - `src/gemini/client.js` - Google Gemini AI integration
  - `src/websocket/handlers.js` - Real-time audio streaming
  - `src/audio/audio-processor.js` - Audio format conversion and processing

**Key Flow**:
1. Twilio receives/initiates call → sends audio via WebSocket
2. SessionManager orchestrates the call lifecycle
3. AudioProcessor converts between Twilio (mulaw) and Gemini (PCM16) formats
4. GeminiClient handles AI conversation with configurable voices/models
5. Responses stream back through WebSocket to caller

## Configuration

All configuration is centralized in `src/config/config.js` which validates environment variables on startup. Key configs:
- `GEMINI_API_KEY` - Required for AI functionality
- `TWILIO_*` - Phone service credentials
- `SUPABASE_*` - Database connection
- `PUBLIC_URL` - Required for Twilio callbacks

## Session Management

Sessions are managed through a sophisticated lifecycle:
- Creation with unique IDs (`session-manager.js`)
- Health monitoring with heartbeats (`health-monitor.js`)
- Automatic recovery on failures (`recovery-manager.js`)
- Proper cleanup on termination (`lifecycle-manager.js`)

Sessions track state transitions and maintain conversation context throughout the call.

## WebSocket Protocol

The application uses WebSocket for real-time audio streaming:
- Twilio Media Streams send audio chunks
- Custom protocol for local audio sessions
- Bidirectional audio flow with format conversion
- Heartbeat monitoring for connection health

## Frontend Integration

The Next.js frontend (`call-center-frontend/`) provides:
- Call management interfaces
- Campaign script editor
- Real-time call monitoring
- Authentication via Supabase

## Testing Approach

- Unit tests via `npm test` (Node.js built-in test runner)
- Test endpoints in `src/api/testing.js` for simulating calls
- Local audio session testing at `/local-audio-session`
- Manual testing interfaces in HTML files

## Production Deployment (from FINAL_STATE.md)

**Backend Service**: Port 3101
**Frontend Service**: Port 3011 (Next.js production)
**Backend API**: https://gemini-api.verduona.com/
**Frontend URL**: https://twilio-gemini.verduona.com/

## Common Development Tasks

When modifying call handling:
1. Check `src/websocket/handlers.js` for WebSocket message handling
2. Review `src/session/session-manager.js` for session lifecycle
3. Test all 4 flows: outbound/inbound × twilio/browser

When working with AI responses:
1. See `src/gemini/client.js` for Gemini integration
2. Configure voices in `src/gemini/voice-manager.js`
3. Adjust models in `src/gemini/model-manager.js`
4. **IMPORTANT**: Use Live API `setup` messages for session initialization, `sendRealtimeInput` for audio
5. **NEVER**: Use `sendClientContent` or `turnComplete` - these break continuous conversation

When debugging audio issues:
1. Browser audio uses PCM16 @ 16kHz (no conversion needed)
2. Twilio audio uses μ-law @ 8kHz (needs conversion)
3. Use `sendBrowserAudioToGemini` for browser audio
4. Use `sendAudioToGemini` for Twilio audio

## Critical Implementation Notes

1. **Audio Methods**: 
   - Browser: `geminiSession.sendRealtimeInput({ media: { data, mimeType } })`
   - Twilio: Same method after μ-law to PCM conversion

2. **Campaign Scripts**: All AI behavior comes from campaign scripts (IDs 1-12)
   - Scripts 1-6: Outbound campaigns
   - Scripts 7-12: Inbound campaigns

3. **WebSocket Flows**:
   - `/media-stream` - Twilio calls (inbound/outbound)
   - `/local-audio-session` - Browser testing (legacy)
   - `/test-outbound` - Outbound testing
   - `/test-inbound` - Inbound testing