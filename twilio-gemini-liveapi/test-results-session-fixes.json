{"timestamp": "2025-07-18T17:25:04.402Z", "serverStatus": "available", "results": {"total": 5, "passed": 2, "failed": 3, "skipped": 0, "errors": [{"test": "Session Fixes", "file": "test/session-handling-fixes.test.js", "code": 1, "stdout": "node:internal/test_runner/runner:294:28)\n    Socket.<anonymous> (node:internal/test_runner/runner:393:15)\n    Socket.emit (node:events:524:28)\n    addChunk (node:internal/streams/readable:561:12)\n    readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    Readable.push (node:internal/streams/readable:392:5)\n    Pipe.onStreamRead (node:internal/stream_base_commons:191:23)\n  ...\n1..1\n# tests 2\n# suites 1\n# pass 1\n# fail 1\n# cancelled 0\n# skipped 0\n# todo 0\n# duration_ms 745.416684\n", "stderr": ""}, {"test": "twilio-validation", "file": "test/twilio-validation.test.js", "code": 1, "stdout": "'subtestsFailed'\n      error: '2 subtests failed'\n      code: 'ERR_TEST_FAILURE'\n      ...\n    1..5\nnot ok 1 - Twilio Webhook Validation Tests\n  ---\n  duration_ms: 18.33712\n  type: 'suite'\n  location: '/home/<USER>/github/verduona-full/twilio-gemini-liveapi/test/twilio-validation.test.js:15:1'\n  failureType: 'subtestsFailed'\n  error: '5 subtests failed'\n  code: 'ERR_TEST_FAILURE'\n  ...\n1..1\n# tests 21\n# suites 6\n# pass 10\n# fail 11\n# cancelled 0\n# skipped 0\n# todo 0\n# duration_ms 106.046251\n", "stderr": ""}, {"test": "session-manager", "file": "test/session-manager.test.js", "code": 1, "stdout": "...\n        1..1\n    ok 6 - memory management\n      ---\n      duration_ms: 3.659539\n      type: 'suite'\n      ...\n    1..6\nnot ok 1 - Session<PERSON>anager\n  ---\n  duration_ms: 17.533941\n  type: 'suite'\n  location: '/home/<USER>/github/verduona-full/twilio-gemini-liveapi/test/session-manager.test.js:5:1'\n  failureType: 'subtestsFailed'\n  error: '1 subtest failed'\n  code: 'ERR_TEST_FAILURE'\n  ...\n1..1\n# tests 8\n# suites 7\n# pass 7\n# fail 1\n# cancelled 0\n# skipped 0\n# todo 0\n# duration_ms 184.436868\n", "stderr": ""}], "performance": {"Session Fixes": 793.200067, "Workflow Integration": 12463.065807, "twilio-validation": 139.01191300000028, "session-manager": 211.56701199999952, "audio-processor": 532.6714630000006}, "startTime": 34.588151, "endTime": null}, "environment": {"nodeVersion": "v20.19.0", "platform": "linux", "serverPort": 3101, "serverUrl": "http://localhost:3101"}, "fixesValidated": {"Twilio Audio Streaming (Sequence Numbers)": true, "Standardized Instruction Sending": true, "Inbound Configuration Loading": true, "Testing Flow Consistency": true, "Security & Cleanup": true}}