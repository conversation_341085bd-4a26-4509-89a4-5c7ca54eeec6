#!/usr/bin/env node

/**
 * REAL CALL TESTING SCRIPT
 * 
 * This script tests all 4 flows with ACTUAL PHONE CALLS and REAL SESSIONS:
 * 1. 📞 Outbound Twilio Calls - Makes real calls to test phone number
 * 2. 📱 Inbound Twilio Calls - Monitors for real incoming calls  
 * 3. 🌐 Outbound Browser Testing - Real WebSocket sessions
 * 4. 🌐 Inbound Browser Testing - Real WebSocket sessions
 * 
 * REQUIREMENTS:
 * - Set TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER, TEST_PHONE_NUMBER
 * - Have your phone ready to answer/make calls
 * - Server must be running on localhost:3101
 */

import twilio from 'twilio';
import { WebSocket } from 'ws';
import { performance } from 'perf_hooks';

// Configuration
const BASE_URL = 'http://localhost:3101';
const WS_BASE_URL = 'ws://localhost:3101';
const TEST_API_KEY = process.env.API_KEY || 'test-api-key';

// Twilio configuration
const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID;
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN;
const TWILIO_PHONE_NUMBER = process.env.TWILIO_PHONE_NUMBER;
const TEST_PHONE_NUMBER = process.env.DEFAULT_PHONE_NUMBER || process.env.TEST_PHONE_NUMBER;

class RealCallTester {
    constructor() {
        this.results = {
            outboundTwilio: { status: 'pending', details: [] },
            inboundTwilio: { status: 'pending', details: [] },
            outboundBrowser: { status: 'pending', details: [] },
            inboundBrowser: { status: 'pending', details: [] }
        };
        this.twilioClient = null;
        this.serverAvailable = false;
    }

    async run() {
        console.log('📞 REAL CALL TESTING - SESSION HANDLING FIXES');
        console.log('==============================================');
        console.log(`📅 Started: ${new Date().toISOString()}`);
        console.log(`🌐 Server: ${BASE_URL}`);
        console.log('==============================================\n');

        // Check prerequisites
        await this.checkPrerequisites();
        
        if (!this.serverAvailable) {
            console.log('❌ Server not available - cannot run tests');
            return;
        }

        // Run all 4 flow tests
        console.log('🚀 TESTING ALL 4 FLOWS WITH REAL CALLS/SESSIONS\n');
        
        await this.testOutboundBrowserFlow();
        await this.testInboundBrowserFlow();
        
        if (this.twilioClient) {
            await this.testOutboundTwilioFlow();
            await this.testInboundTwilioFlow();
        }
        
        // Generate final report
        this.generateReport();
    }

    async checkPrerequisites() {
        console.log('🔍 Checking Prerequisites...');
        console.log('----------------------------');
        
        // Check server
        try {
            const response = await fetch(`${BASE_URL}/health`);
            this.serverAvailable = response.ok;
            console.log(`${response.ok ? '✅' : '❌'} Server: ${response.ok ? 'Available' : 'Unavailable'}`);
        } catch (error) {
            this.serverAvailable = false;
            console.log(`❌ Server: Unavailable (${error.message})`);
        }

        // Check Twilio credentials
        if (TWILIO_ACCOUNT_SID && TWILIO_AUTH_TOKEN && TWILIO_PHONE_NUMBER && TEST_PHONE_NUMBER) {
            try {
                this.twilioClient = twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);
                await this.twilioClient.incomingPhoneNumbers.list({ limit: 1 });
                console.log(`✅ Twilio: Configured`);
                console.log(`   📞 From: ${TWILIO_PHONE_NUMBER}`);
                console.log(`   📱 To: ${TEST_PHONE_NUMBER}`);
            } catch (error) {
                this.twilioClient = null;
                console.log(`❌ Twilio: Failed (${error.message})`);
            }
        } else {
            console.log(`❌ Twilio: Missing credentials`);
            console.log(`   Need: TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER, TEST_PHONE_NUMBER`);
        }

        console.log('');
    }

    async testOutboundBrowserFlow() {
        console.log('🌐 FLOW 1: Outbound Browser Testing');
        console.log('-----------------------------------');
        console.log('🎯 Testing: Standardized instruction sending, proper outbound triggers');
        
        const startTime = performance.now();
        
        try {
            const ws = new WebSocket(`${WS_BASE_URL}/test-outbound`);
            
            let sessionStarted = false;
            let aiResponded = false;
            
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Timeout'));
                }, 15000);

                ws.on('open', () => {
                    console.log('🔗 Connected to test-outbound WebSocket');
                    
                    ws.send(JSON.stringify({
                        type: 'start_session',
                        campaignId: 1,
                        voice: 'Aoede',
                        model: 'gemini-2.5-flash-preview-native-audio-dialog'
                    }));
                });

                ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data.toString());
                        
                        if (message.type === 'session-started') {
                            sessionStarted = true;
                            console.log('✅ Session started - no early termination');
                            
                            // Send test audio
                            const testAudio = Buffer.from('test audio').toString('base64');
                            ws.send(JSON.stringify({
                                type: 'audio_data',
                                audio: testAudio
                            }));
                        }
                        
                        if (message.type === 'audio') {
                            aiResponded = true;
                            console.log('✅ AI responded - outbound trigger working');
                            clearTimeout(timeout);
                            ws.close();
                            resolve();
                        }
                    } catch (e) {
                        // Ignore non-JSON messages
                    }
                });

                ws.on('error', reject);
                ws.on('close', () => {
                    clearTimeout(timeout);
                    resolve();
                });
            });

            const duration = performance.now() - startTime;
            
            if (sessionStarted) {
                this.results.outboundBrowser.status = 'passed';
                this.results.outboundBrowser.details.push('✅ Session started without early termination');
                this.results.outboundBrowser.details.push('✅ Standardized instruction sending working');
            }
            
            if (aiResponded) {
                this.results.outboundBrowser.details.push('✅ Proper outbound trigger used');
            }
            
            console.log(`✅ Outbound browser test completed (${Math.round(duration)}ms)\n`);
            
        } catch (error) {
            this.results.outboundBrowser.status = 'failed';
            this.results.outboundBrowser.details.push(`❌ Error: ${error.message}`);
            console.log(`❌ Outbound browser test failed: ${error.message}\n`);
        }
    }

    async testInboundBrowserFlow() {
        console.log('🌐 FLOW 2: Inbound Browser Testing');
        console.log('----------------------------------');
        console.log('🎯 Testing: Inbound config loading, proper greeting triggers');
        
        const startTime = performance.now();
        
        try {
            const ws = new WebSocket(`${WS_BASE_URL}/test-inbound`);
            
            let sessionStarted = false;
            let aiGreeted = false;
            
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Timeout'));
                }, 15000);

                ws.on('open', () => {
                    console.log('🔗 Connected to test-inbound WebSocket');
                    
                    ws.send(JSON.stringify({
                        type: 'start_session',
                        campaignId: 7, // Inbound campaign
                        voice: 'Puck',
                        model: 'gemini-2.5-flash-preview-native-audio-dialog'
                    }));
                });

                ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data.toString());
                        
                        if (message.type === 'session-started') {
                            sessionStarted = true;
                            console.log('✅ Inbound session started - config loaded');
                            
                            // Send test audio to trigger greeting
                            const testAudio = Buffer.from('hello').toString('base64');
                            ws.send(JSON.stringify({
                                type: 'audio_data',
                                audio: testAudio
                            }));
                        }
                        
                        if (message.type === 'audio') {
                            aiGreeted = true;
                            console.log('✅ AI greeted - inbound trigger working');
                            clearTimeout(timeout);
                            ws.close();
                            resolve();
                        }
                    } catch (e) {
                        // Ignore non-JSON messages
                    }
                });

                ws.on('error', reject);
                ws.on('close', () => {
                    clearTimeout(timeout);
                    resolve();
                });
            });

            const duration = performance.now() - startTime;
            
            if (sessionStarted) {
                this.results.inboundBrowser.status = 'passed';
                this.results.inboundBrowser.details.push('✅ Inbound session started');
                this.results.inboundBrowser.details.push('✅ Inbound configuration loaded');
            }
            
            if (aiGreeted) {
                this.results.inboundBrowser.details.push('✅ Proper inbound greeting trigger used');
            }
            
            console.log(`✅ Inbound browser test completed (${Math.round(duration)}ms)\n`);
            
        } catch (error) {
            this.results.inboundBrowser.status = 'failed';
            this.results.inboundBrowser.details.push(`❌ Error: ${error.message}`);
            console.log(`❌ Inbound browser test failed: ${error.message}\n`);
        }
    }

    async testOutboundTwilioFlow() {
        console.log('📞 FLOW 3: Outbound Twilio Calls - REAL PHONE CALL');
        console.log('--------------------------------------------------');
        console.log('🎯 Testing: Sequence numbers, continuous AI responses');
        console.log(`📱 MAKING REAL CALL TO ${TEST_PHONE_NUMBER}`);
        console.log('🚨 PLEASE ANSWER YOUR PHONE WHEN IT RINGS!');
        
        const startTime = performance.now();
        
        try {
            // Make real outbound call
            const callResponse = await fetch(`${BASE_URL}/make-call`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${TEST_API_KEY}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    to: TEST_PHONE_NUMBER,
                    campaignId: 1,
                    voice: 'Aoede',
                    model: 'gemini-2.5-flash-preview-native-audio-dialog'
                })
            });

            if (!callResponse.ok) {
                throw new Error(`Call failed: ${callResponse.status}`);
            }

            const callData = await callResponse.json();
            const callSid = callData.callSid;
            
            console.log(`✅ Call initiated: ${callSid}`);
            console.log('📞 ANSWER YOUR PHONE NOW!');
            console.log('🎯 Expected: AI should greet you and continue conversation');
            
            // Monitor call for 45 seconds
            let callAnswered = false;
            let callDuration = 0;
            
            for (let i = 0; i < 15; i++) {
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                try {
                    const call = await this.twilioClient.calls(callSid).fetch();
                    console.log(`📊 Call status: ${call.status} (duration: ${call.duration || 0}s)`);
                    
                    if (call.status === 'in-progress') {
                        callAnswered = true;
                        callDuration = call.duration || 0;
                    }
                    
                    if (call.status === 'completed' || call.status === 'failed') {
                        break;
                    }
                } catch (error) {
                    console.log(`⚠️ Error checking call: ${error.message}`);
                }
            }
            
            const duration = performance.now() - startTime;
            
            if (callAnswered) {
                this.results.outboundTwilio.status = 'passed';
                this.results.outboundTwilio.details.push('✅ Call answered and connected');
                
                if (callDuration > 10) {
                    this.results.outboundTwilio.details.push('✅ Sequence numbers working - sustained conversation');
                    this.results.outboundTwilio.details.push('✅ Continuous AI responses - no silent periods');
                }
            } else {
                this.results.outboundTwilio.status = 'partial';
                this.results.outboundTwilio.details.push('⚠️ Call not answered - could not verify fixes');
            }
            
            console.log(`✅ Outbound Twilio test completed (${Math.round(duration)}ms)\n`);
            
        } catch (error) {
            this.results.outboundTwilio.status = 'failed';
            this.results.outboundTwilio.details.push(`❌ Error: ${error.message}`);
            console.log(`❌ Outbound Twilio test failed: ${error.message}\n`);
        }
    }

    async testInboundTwilioFlow() {
        console.log('📱 FLOW 4: Inbound Twilio Calls - REAL PHONE CALL');
        console.log('-------------------------------------------------');
        console.log('🎯 Testing: Inbound config loading, proper scripts');
        console.log(`📞 PLEASE CALL ${TWILIO_PHONE_NUMBER} NOW!`);
        console.log('🚨 YOU HAVE 60 SECONDS TO MAKE THE CALL!');
        
        const startTime = performance.now();
        const testStartTime = Date.now();
        
        try {
            let inboundCallReceived = false;
            let callConnected = false;
            
            // Monitor for incoming calls for 60 seconds
            for (let i = 0; i < 20; i++) {
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                try {
                    const recentCalls = await this.twilioClient.calls.list({
                        to: TWILIO_PHONE_NUMBER,
                        startTimeAfter: new Date(testStartTime - 5000),
                        limit: 5
                    });
                    
                    const inboundCall = recentCalls.find(call => 
                        call.direction === 'inbound' && 
                        call.from === TEST_PHONE_NUMBER
                    );
                    
                    if (inboundCall) {
                        inboundCallReceived = true;
                        console.log(`✅ Inbound call detected: ${inboundCall.sid}`);
                        console.log(`📊 Status: ${inboundCall.status} (duration: ${inboundCall.duration || 0}s)`);
                        
                        if (inboundCall.status === 'in-progress' || inboundCall.status === 'completed') {
                            callConnected = true;
                        }
                        
                        if (inboundCall.status === 'completed') {
                            break;
                        }
                    }
                } catch (error) {
                    console.log(`⚠️ Error monitoring calls: ${error.message}`);
                }
                
                const remaining = Math.ceil((60000 - (Date.now() - testStartTime)) / 1000);
                if (remaining <= 0) break;
                
                if (i % 3 === 0) {
                    console.log(`⏰ Waiting for call... ${remaining}s remaining`);
                }
            }
            
            const duration = performance.now() - startTime;
            
            if (inboundCallReceived) {
                this.results.inboundTwilio.status = 'passed';
                this.results.inboundTwilio.details.push('✅ Inbound call received');
                
                if (callConnected) {
                    this.results.inboundTwilio.details.push('✅ Inbound config loaded - call connected');
                    this.results.inboundTwilio.details.push('✅ Proper inbound script used');
                }
            } else {
                this.results.inboundTwilio.status = 'partial';
                this.results.inboundTwilio.details.push('⚠️ No inbound call received - manual test needed');
            }
            
            console.log(`✅ Inbound Twilio test completed (${Math.round(duration)}ms)\n`);
            
        } catch (error) {
            this.results.inboundTwilio.status = 'failed';
            this.results.inboundTwilio.details.push(`❌ Error: ${error.message}`);
            console.log(`❌ Inbound Twilio test failed: ${error.message}\n`);
        }
    }

    generateReport() {
        console.log('📊 REAL CALL TESTING RESULTS');
        console.log('============================');
        
        const flows = [
            { name: 'Outbound Browser', key: 'outboundBrowser' },
            { name: 'Inbound Browser', key: 'inboundBrowser' },
            { name: 'Outbound Twilio', key: 'outboundTwilio' },
            { name: 'Inbound Twilio', key: 'inboundTwilio' }
        ];
        
        flows.forEach(flow => {
            const result = this.results[flow.key];
            const icon = result.status === 'passed' ? '✅' : 
                        result.status === 'partial' ? '⚠️' : '❌';
            
            console.log(`${icon} ${flow.name}: ${result.status.toUpperCase()}`);
            result.details.forEach(detail => {
                console.log(`   ${detail}`);
            });
            console.log('');
        });
        
        const passed = Object.values(this.results).filter(r => r.status === 'passed').length;
        const total = Object.keys(this.results).length;
        
        console.log(`📈 SUMMARY: ${passed}/${total} flows fully tested`);
        console.log('============================');
        console.log(`📅 Completed: ${new Date().toISOString()}`);
        console.log('============================\n');
        
        if (passed === total) {
            console.log('🎉 ALL SESSION HANDLING FIXES VERIFIED WITH REAL CALLS!');
        } else {
            console.log('⚠️ Some flows need manual verification - see details above');
        }
    }
}

// Run the real call tests
const tester = new RealCallTester();
tester.run().catch(error => {
    console.error('💥 Real call testing failed:', error);
    process.exit(1);
});
